'use server'

import { createServerClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'

export async function getUser() {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)
    try {
        const { data: { user }, error } = await supabase.auth.getUser()
        if (error) {
            console.error('Error getting user:', error)
            return null
        }
        return user
    } catch (error: any) {
        console.error('Error getting user:', error)
        return null
    }
}

export async function signOut() {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)
    try {
        const { error } = await supabase.auth.signOut()
        if (error) {
            console.error('Error signing out:', error)
            throw error
        }
    } catch (error: any) {
        console.error('Error signing out:', error)
        throw error
    }
} 