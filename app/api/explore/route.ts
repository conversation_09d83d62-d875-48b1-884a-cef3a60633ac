/**
 * @swagger
 * /api/explore:
 *   get:
 *     tags: [Explore]
 *     summary: GET /api/explore
 *     description: 探索相關的 API
 *     responses:
 *       200:
 *         description: 成功回應
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       500:
 *         description: 伺服器錯誤
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

// 添加記憶體快取來避免重複查詢
const cache = new Map()
const CACHE_DURATION = 5 * 60 * 1000 // 5分鐘快取

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)
    const searchParams = request.nextUrl.searchParams

    // Parse query parameters
    const type = searchParams.get("type") || "all"
    const sort = searchParams.get("sort") || "trending"
    const page = Number.parseInt(searchParams.get("page") || "1", 10)
    const limit = Number.parseInt(searchParams.get("limit") || "10", 10)

    // Filter parameters
    const semanticTypes = searchParams.get("semanticTypes")?.split(",").filter(Boolean) || []
    const topics = searchParams.get("topics")?.split(",").filter(Boolean) || []
    const query = searchParams.get("q") || ""

    console.log("Explore API request:", { type, sort, page, limit, semanticTypes, topics, query })

    // 建立快取鍵
    const cacheKey = `explore:${type}:${sort}:${page}:${limit}:${semanticTypes.join(',')}:${topics.join(',')}:${query}`

    // 檢查快取
    const cached = cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return NextResponse.json(cached.data)
    }

    let results = []
    let total = 0
    let hasMoreData = false

    if (type === "all") {
      // 對於混合類型，並行獲取兩種內容
      const [cardsResult, threadsResult] = await Promise.all([
        fetchCardsOptimized({
          supabase,
          semanticTypes,
          topics,
          query,
          sort,
          page,
          limit: Math.ceil(limit / 2),
        }),
        fetchThreadsOptimized({
          supabase,
          semanticTypes,
          topics,
          query,
          sort,
          page,
          limit: Math.floor(limit / 2),
        }),
      ])

      results = [...cardsResult.data, ...threadsResult.data]

      // 根據排序重新排列
      if (sort === "latest") {
        results.sort((a, b) => new Date(b.rawTimestamp).getTime() - new Date(a.rawTimestamp).getTime())
      } else if (sort === "most_liked") {
        results.sort((a, b) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
      }

      // 對於混合類型，如果任一類型返回滿額結果，可能還有更多
      const cardsHasMore = results.filter(r => r.contentType === "viewpoint").length === Math.ceil(limit / 2)
      const threadsHasMore = results.filter(r => r.contentType === "thread").length === Math.floor(limit / 2)
      hasMoreData = cardsHasMore || threadsHasMore

      total = hasMoreData ? (page * limit + 1) : ((page - 1) * limit + results.length)
    } else if (type === "viewpoint") {
      const cardsResult = await fetchCardsOptimized({
        supabase,
        semanticTypes,
        topics,
        query,
        sort,
        page,
        limit,
      })
      results = cardsResult.data
      total = cardsResult.total

      // 對於單一類型，使用傳統的分頁邏輯
      hasMoreData = results.length === limit && (page * limit) < total
    } else {
      const threadsResult = await fetchThreadsOptimized({
        supabase,
        semanticTypes,
        topics,
        query,
        sort,
        page,
        limit,
      })
      results = threadsResult.data
      total = threadsResult.total

      // 對於單一類型，使用傳統的分頁邏輯
      hasMoreData = results.length === limit && (page * limit) < total
    }

    const response = {
      success: true,
      data: results,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: hasMoreData,
      },
    }

    // 快取結果
    cache.set(cacheKey, {
      data: response,
      timestamp: Date.now()
    })

    // 清理過期快取（每 100 次請求清理一次）
    if (Math.random() < 0.01) {
      const now = Date.now()
      for (const [key, value] of cache.entries()) {
        if (now - value.timestamp > CACHE_DURATION) {
          cache.delete(key)
        }
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Explore API error:", error)
    console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace")
    return NextResponse.json({
      success: false,
      error: "Failed to fetch explore data",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}

// 優化的卡片獲取函數
async function fetchCardsOptimized({
  supabase,
  semanticTypes,
  topics,
  query,
  sort,
  page,
  limit
}: {
  supabase: any;
  semanticTypes: string[];
  topics: string[];
  query: string;
  sort: string;
  page: number;
  limit: number
}) {
  try {
    const offset = (page - 1) * limit

    // 使用更簡潔的查詢，一次性獲取所有必要資料
    let cardsQuery = supabase
      .from("cards")
      .select(`
        id, 
        title, 
        content, 
        semantic_type, 
        contribution_type, 
        original_author, 
        original_url, 
        created_at,
        profiles:author_id (id, name, avatar),
        card_topics!left (topics:topic_id(id, name)),
        card_subtopics!left (subtopics:subtopic_id(id, name))
      `, { count: "exact" })
      .eq("status", "published")

    // 應用語義類型過濾器
    if (semanticTypes.length > 0) {
      cardsQuery = cardsQuery.in("semantic_type", semanticTypes)
    }

    // 應用搜尋過濾器
    if (query) {
      cardsQuery = cardsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
    }

    // 如果有主題過濾，需要特殊處理
    if (topics.length > 0) {
      // 先獲取所有符合條件的卡片（不包括分頁）來進行主題過濾
      let allCardsQuery = supabase
        .from("cards")
        .select(`
          id, 
          title, 
          content, 
          semantic_type, 
          contribution_type, 
          original_author, 
          original_url, 
          created_at,
          profiles:author_id (id, name, avatar),
          card_topics!left (topics:topic_id(id, name)),
          card_subtopics!left (subtopics:subtopic_id(id, name))
        `)
        .eq("status", "published")

      // 應用相同的過濾器
      if (semanticTypes.length > 0) {
        allCardsQuery = allCardsQuery.in("semantic_type", semanticTypes)
      }
      if (query) {
        allCardsQuery = allCardsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
      }

      // 應用排序
      if (sort === "latest") {
        allCardsQuery = allCardsQuery.order("created_at", { ascending: false })
      } else {
        allCardsQuery = allCardsQuery.order("created_at", { ascending: false })
      }

      const { data: allCards, error: allCardsError } = await allCardsQuery

      if (allCardsError) {
        console.error("All cards query error:", allCardsError)
        throw allCardsError
      }

      if (!allCards || allCards.length === 0) {
        return { data: [], total: 0 }
      }

      // 格式化所有卡片並應用主題過濾
      let formattedAllCards = allCards.map((card: any) => ({
        id: card.id,
        title: card.title,
        content: card.content,
        semantic_type: card.semantic_type,
        contribution_type: card.contribution_type,
        original_author: card.original_author,
        original_url: card.original_url,
        created_at: card.created_at,
        profiles: card.profiles,
        topics: (card.card_topics || []).map((ct: any) => ct.topics?.name).filter(Boolean),
        subtopics: (card.card_subtopics || []).map((cs: any) => cs.subtopics?.name).filter(Boolean),
      }))

      // 應用主題和子主題過濾
      formattedAllCards = formattedAllCards.filter((card: any) =>
        // 在主題中搜索
        card.topics.some((topic: string) =>
          topics.some((filterTopic: string) =>
            topic.toLowerCase().includes(filterTopic.toLowerCase()) ||
            filterTopic.toLowerCase().includes(topic.toLowerCase())
          )
        ) ||
        // 在子主題中搜索
        card.subtopics.some((subtopic: string) =>
          topics.some((filterTopic: string) =>
            subtopic.toLowerCase().includes(filterTopic.toLowerCase()) ||
            filterTopic.toLowerCase().includes(subtopic.toLowerCase())
          )
        )
      )

      // 應用分頁到過濾後的結果
      const paginatedCards = formattedAllCards.slice(offset, offset + limit)

      if (paginatedCards.length === 0) {
        return { data: [], total: formattedAllCards.length }
      }

      // 批量獲取統計資料
      const cardIds = paginatedCards.map((card: any) => card.id)
      const [reactionsData, commentsData] = await Promise.all([
        supabase
          .from("reactions")
          .select("item_id")
          .eq("item_type", "card")
          .eq("reaction_type", "like")
          .in("item_id", cardIds),

        supabase
          .from("comments")
          .select("root_item_id")
          .eq("root_item_type", "card")
          .in("root_item_id", cardIds)
      ])

      // 建立統計映射
      const likesMap = new Map()
      const commentsMap = new Map()

      if (reactionsData.data) {
        reactionsData.data.forEach((reaction: any) => {
          likesMap.set(reaction.item_id, (likesMap.get(reaction.item_id) || 0) + 1)
        })
      }

      if (commentsData.data) {
        commentsData.data.forEach((comment: any) => {
          commentsMap.set(comment.root_item_id, (commentsMap.get(comment.root_item_id) || 0) + 1)
        })
      }

      // 最終格式化
      const finalFormattedCards = paginatedCards.map((card: any) => ({
        id: card.id,
        title: card.title,
        content: card.content,
        contentType: "viewpoint",
        semanticType: card.semantic_type,
        contribution_type: card.contribution_type,
        originalAuthor: card.original_author,
        originalSource: card.original_url,
        author: {
          id: card.profiles?.id,
          name: card.profiles?.name,
          avatar: card.profiles?.avatar,
        },
        timestamp: new Date(card.created_at).toLocaleDateString("zh-TW"),
        rawTimestamp: card.created_at,
        topics: card.topics,
        subtopics: card.subtopics,
        stats: {
          likes: likesMap.get(card.id) || 0,
          comments: commentsMap.get(card.id) || 0,
        },
      }))

      // 如果需要按點讚數排序
      if (sort === "most_liked") {
        finalFormattedCards.sort((a: any, b: any) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
      }

      return { data: finalFormattedCards, total: formattedAllCards.length }
    }

    // 如果沒有主題過濾，使用原來的邏輯
    // 應用排序
    if (sort === "latest") {
      cardsQuery = cardsQuery.order("created_at", { ascending: false })
    } else {
      cardsQuery = cardsQuery.order("created_at", { ascending: false })
    }

    // 應用分頁
    cardsQuery = cardsQuery.range(offset, offset + limit - 1)

    const { data: cards, error, count } = await cardsQuery

    if (error) {
      // 如果是分頁超出範圍的錯誤，返回空結果而不是拋出錯誤
      if (error.code === "PGRST103") {
        console.log("Offset out of range, returning empty result")
        return { data: [], total: 0 }
      }
      console.error("Cards query error:", error)
      throw error
    }

    const totalCards = count || 0

    if (!cards || cards.length === 0) {
      return { data: [], total: totalCards }
    }

    // 批量獲取統計資料
    const cardIds = cards.map((card: any) => card.id)
    const [reactionsData, commentsData] = await Promise.all([
      supabase
        .from("reactions")
        .select("item_id")
        .eq("item_type", "card")
        .eq("reaction_type", "like")
        .in("item_id", cardIds),

      supabase
        .from("comments")
        .select("root_item_id")
        .eq("root_item_type", "card")
        .in("root_item_id", cardIds)
    ])

    // 建立統計映射
    const likesMap = new Map()
    const commentsMap = new Map()

    if (reactionsData.data) {
      reactionsData.data.forEach((reaction: any) => {
        likesMap.set(reaction.item_id, (likesMap.get(reaction.item_id) || 0) + 1)
      })
    }

    if (commentsData.data) {
      commentsData.data.forEach((comment: any) => {
        commentsMap.set(comment.root_item_id, (commentsMap.get(comment.root_item_id) || 0) + 1)
      })
    }

    // 格式化卡片資料
    let formattedCards = cards.map((card: any) => ({
      id: card.id,
      title: card.title,
      content: card.content,
      contentType: "viewpoint",
      semanticType: card.semantic_type,
      contribution_type: card.contribution_type,
      originalAuthor: card.original_author,
      originalSource: card.original_url,
      author: {
        id: card.profiles?.id,
        name: card.profiles?.name,
        avatar: card.profiles?.avatar,
      },
      timestamp: new Date(card.created_at).toLocaleDateString("zh-TW"),
      rawTimestamp: card.created_at,
      topics: (card.card_topics || []).map((ct: any) => ct.topics?.name).filter(Boolean),
      subtopics: (card.card_subtopics || []).map((cs: any) => cs.subtopics?.name).filter(Boolean),
      stats: {
        likes: likesMap.get(card.id) || 0,
        comments: commentsMap.get(card.id) || 0,
      },
    }))

    // 如果需要按點讚數排序
    if (sort === "most_liked") {
      formattedCards.sort((a: any, b: any) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
    }

    return { data: formattedCards, total: totalCards }
  } catch (error) {
    console.error("fetchCardsOptimized error:", error)
    throw error
  }
}

// 優化的討論串獲取函數
async function fetchThreadsOptimized({
  supabase,
  semanticTypes,
  topics,
  query,
  sort,
  page,
  limit
}: {
  supabase: any;
  semanticTypes: string[];
  topics: string[];
  query: string;
  sort: string;
  page: number;
  limit: number
}) {
  try {
    const offset = (page - 1) * limit

    // 使用更簡潔的查詢
    let threadsQuery = supabase
      .from("threads")
      .select(`
          id, 
          title, 
          content, 
          semantic_type, 
          created_at,
          author_id,
          profiles:author_id (id, name, avatar),
          thread_topics!left (topics:topic_id(id, name)),
          thread_subtopics!left (subtopics:subtopic_id(id, name))
        `, { count: "exact" })
      .eq("status", "published")

    // 應用語義類型過濾器
    if (semanticTypes.length > 0) {
      threadsQuery = threadsQuery.in("semantic_type", semanticTypes)
    }

    // 應用搜尋過濾器
    if (query) {
      threadsQuery = threadsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
    }

    // 如果有主題過濾，需要特殊處理
    if (topics.length > 0) {
      // 先獲取所有符合條件的討論串（不包括分頁）來進行主題過濾
      let allThreadsQuery = supabase
        .from("threads")
        .select(`
            id, 
            title, 
            content, 
            semantic_type, 
            created_at,
            author_id,
            profiles:author_id (id, name, avatar),
            thread_topics!left (topics:topic_id(id, name)),
            thread_subtopics!left (subtopics:subtopic_id(id, name))
          `)
        .eq("status", "published")

      // 應用相同的過濾器
      if (semanticTypes.length > 0) {
        allThreadsQuery = allThreadsQuery.in("semantic_type", semanticTypes)
      }
      if (query) {
        allThreadsQuery = allThreadsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
      }

      // 應用排序
      if (sort === "latest") {
        allThreadsQuery = allThreadsQuery.order("created_at", { ascending: false })
      } else {
        allThreadsQuery = allThreadsQuery.order("created_at", { ascending: false })
      }

      const { data: allThreads, error: allThreadsError } = await allThreadsQuery

      if (allThreadsError) {
        console.error("All threads query error:", allThreadsError)
        throw allThreadsError
      }

      if (!allThreads || allThreads.length === 0) {
        return { data: [], total: 0 }
      }

      // 格式化所有討論串並應用主題過濾
      let formattedAllThreads = allThreads.map((thread: any) => ({
        id: thread.id,
        title: thread.title,
        content: thread.content,
        semantic_type: thread.semantic_type,
        created_at: thread.created_at,
        author_id: thread.author_id,
        profiles: thread.profiles,
        topics: (thread.thread_topics || []).map((tt: any) => tt.topics?.name).filter(Boolean),
        tags: (thread.thread_subtopics || []).map((ts: any) => ts.subtopics?.name).filter(Boolean),
      }))

      // 應用主題和子主題過濾
      formattedAllThreads = formattedAllThreads.filter((thread: any) =>
        // 在主題中搜索
        thread.topics.some((topic: string) =>
          topics.some((filterTopic: string) =>
            topic.toLowerCase().includes(filterTopic.toLowerCase()) ||
            filterTopic.toLowerCase().includes(topic.toLowerCase())
          )
        ) ||
        // 在子主題(tags)中搜索
        thread.tags.some((tag: string) =>
          topics.some((filterTopic: string) =>
            tag.toLowerCase().includes(filterTopic.toLowerCase()) ||
            filterTopic.toLowerCase().includes(tag.toLowerCase())
          )
        )
      )

      // 應用分頁到過濾後的結果
      const paginatedThreads = formattedAllThreads.slice(offset, offset + limit)

      if (paginatedThreads.length === 0) {
        return { data: [], total: formattedAllThreads.length }
      }

      // 批量獲取統計資料
      const threadIds = paginatedThreads.map((thread: any) => thread.id)
      const [reactionsData, repliesData] = await Promise.all([
        supabase
          .from("reactions")
          .select("item_id")
          .eq("item_type", "thread")
          .eq("reaction_type", "like")
          .in("item_id", threadIds),

        supabase
          .from("comments")
          .select("root_item_id")
          .eq("root_item_type", "thread")
          .in("root_item_id", threadIds)
      ])

      // 建立統計映射
      const likesMap = new Map()
      const repliesMap = new Map()

      if (reactionsData.data) {
        reactionsData.data.forEach((reaction: any) => {
          likesMap.set(reaction.item_id, (likesMap.get(reaction.item_id) || 0) + 1)
        })
      }

      if (repliesData.data) {
        repliesData.data.forEach((reply: any) => {
          repliesMap.set(reply.root_item_id, (repliesMap.get(reply.root_item_id) || 0) + 1)
        })
      }

      // 最終格式化
      const finalFormattedThreads = paginatedThreads.map((thread: any) => ({
        id: thread.id,
        title: thread.title,
        content: thread.content,
        contentType: "thread",
        semanticType: thread.semantic_type || "discussion",
        author: {
          id: thread.profiles?.id || "unknown",
          name: thread.profiles?.name || "未知作者",
          avatar: thread.profiles?.avatar || null,
        },
        timestamp: new Date(thread.created_at).toLocaleDateString("zh-TW"),
        rawTimestamp: thread.created_at,
        topics: thread.topics,
        tags: thread.tags,
        stats: {
          likes: likesMap.get(thread.id) || 0,
          replies: repliesMap.get(thread.id) || 0,
        },
      }))

      // 如果需要按點讚數排序
      if (sort === "most_liked") {
        finalFormattedThreads.sort((a: any, b: any) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
      }

      return { data: finalFormattedThreads, total: formattedAllThreads.length }
    }

    // 如果沒有主題過濾，使用原來的邏輯
    // 應用排序
    if (sort === "latest") {
      threadsQuery = threadsQuery.order("created_at", { ascending: false })
    } else {
      threadsQuery = threadsQuery.order("created_at", { ascending: false })
    }

    // 應用分頁
    threadsQuery = threadsQuery.range(offset, offset + limit - 1)

    const { data: threads, error, count } = await threadsQuery

    if (error) {
      // 如果是分頁超出範圍的錯誤，返回空結果而不是拋出錯誤
      if (error.code === "PGRST103") {
        console.log("Threads offset out of range, returning empty result")
        return { data: [], total: 0 }
      }
      console.error("Threads query error:", error)
      throw error
    }

    const totalThreads = count || 0

    if (!threads || threads.length === 0) {
      return { data: [], total: totalThreads }
    }

    // 批量獲取統計資料
    const threadIds = threads.map((thread: any) => thread.id)
    const [reactionsData, repliesData] = await Promise.all([
      supabase
        .from("reactions")
        .select("item_id")
        .eq("item_type", "thread")
        .eq("reaction_type", "like")
        .in("item_id", threadIds),

      supabase
        .from("comments")
        .select("root_item_id")
        .eq("root_item_type", "thread")
        .in("root_item_id", threadIds)
    ])

    // 建立統計映射
    const likesMap = new Map()
    const repliesMap = new Map()

    if (reactionsData.data) {
      reactionsData.data.forEach((reaction: any) => {
        likesMap.set(reaction.item_id, (likesMap.get(reaction.item_id) || 0) + 1)
      })
    }

    if (repliesData.data) {
      repliesData.data.forEach((reply: any) => {
        repliesMap.set(reply.root_item_id, (repliesMap.get(reply.root_item_id) || 0) + 1)
      })
    }

    // 格式化討論串資料
    let formattedThreads = threads.map((thread: any) => ({
      id: thread.id,
      title: thread.title,
      content: thread.content,
      contentType: "thread",
      semanticType: thread.semantic_type || "discussion",
      author: {
        id: thread.profiles?.id || "unknown",
        name: thread.profiles?.name || "未知作者",
        avatar: thread.profiles?.avatar || null,
      },
      timestamp: new Date(thread.created_at).toLocaleDateString("zh-TW"),
      rawTimestamp: thread.created_at,
      topics: (thread.thread_topics || []).map((tt: any) => tt.topics?.name).filter(Boolean),
      tags: (thread.thread_subtopics || []).map((ts: any) => ts.subtopics?.name).filter(Boolean),
      stats: {
        likes: likesMap.get(thread.id) || 0,
        replies: repliesMap.get(thread.id) || 0,
      },
    }))

    // 如果需要按點讚數排序
    if (sort === "most_liked") {
      formattedThreads.sort((a: any, b: any) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
    }

    return { data: formattedThreads, total: formattedThreads.length }
  } catch (error) {
    console.error("fetchThreadsOptimized error:", error)
    throw error
  }
}
