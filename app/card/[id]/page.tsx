import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getCardById } from "@/lib/card-service"
import { CardDetailContent } from "@/components/card-detail-content"
import { Suspense } from "react"
import { cookies } from "next/headers"
import { createServerClient } from "@/lib/supabase/server"
import type { Card, Profile, Topic, Subtopic } from "@/lib/types"

// 從 content-card.tsx 導入 ContentStats 類型
interface ContentStats {
  likes?: number
  dislikes?: number
  comments?: number
  replies?: number
  views?: number
  bookmarks?: number
}

// 為 CardDetailContent 組件創建一個自定義類型
interface CardDetailData {
  id: string | number
  title: string
  content: string
  author_id: string
  card_type: string
  semantic_type: string
  contribution_type: string
  original_author?: string
  original_url?: string
  created_at: string
  updated_at: string
  author?: Profile
  topics?: Topic[]
  subtopics?: Subtopic[]
  likes_count: number
  dislikes_count: number
  stats?: ContentStats
  comments?: any[]
}

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

interface CardPageProps {
  params: Promise<{
    id: string
  }>
}

// 動態生成元數據
export async function generateMetadata({ params }: CardPageProps): Promise<Metadata> {
  try {
    const { id } = await params
    const card = await getCardById(id)

    if (!card) {
      return {
        title: "卡片不存在",
        description: "找不到請求的卡片",
      }
    }

    return {
      title: `${card.title} | AILogora`,
      description: typeof card.content === "string" ? card.content.substring(0, 160) : "卡片內容",
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "載入中... | AILogora",
      description: "正在載入卡片內容",
    }
  }
}

// 加載中組件
function CardLoading() {
  return (
    <div className="space-y-6">
      {/* 麵包屑導航 - 加載中狀態 */}
      <div className="flex items-center text-sm text-muted-foreground mb-4">
        <div className="h-4 w-20 bg-gray-200 rounded"></div>
        <div className="h-4 w-4 mx-2 bg-gray-200 rounded"></div>
        <div className="h-4 w-24 bg-gray-200 rounded"></div>
      </div>

      {/* 卡片內容 - 加載中狀態 */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="h-10 bg-gray-200 rounded w-1/4"></div>
      </div>

      {/* 留言區 - 加載中狀態 */}
      <div className="pt-4">
        <div className="flex justify-between items-center border-b pb-1 mb-4">
          <div className="h-6 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
      </div>
    </div>
  )
}

// 錯誤組件
function CardError({ error }: { error: Error }) {
  return (
    <div className="space-y-6">
      <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <h2 className="text-lg font-semibold text-red-800 mb-2">載入卡片時出現錯誤</h2>
        <p className="text-red-700">{error.message}</p>
      </div>
      <div className="mt-4">
        <a href="/" className="text-blue-600 hover:underline">
          返回首頁
        </a>
      </div>
    </div>
  )
}

export default async function CardPage({ params }: CardPageProps) {
  try {
    const { id } = await params
    const card = await getCardById(id)

    if (!card) {
      notFound()
    }

    // 獲取卡片的讚數和倒讚數
    const cookieStore = cookies()
    const supabase = await createServerClient(cookieStore)

    let likes_count = 0
    let dislikes_count = 0

    // 檢查 ID 是否為有效的 UUID
    if (isValidUUID(id)) {
      // 獲取點讚數
      const { data: likeData, error: likeError } = await supabase
        .from("reactions")
        .select("id")
        .eq("item_type", "card")
        .eq("item_id", id)
        .eq("reaction_type", "like")

      if (likeError) {
        console.error("獲取點讚數時出錯:", likeError)
      } else {
        likes_count = likeData ? likeData.length : 0
      }

      // 獲取倒讚數
      const { data: dislikeData, error: dislikeError } = await supabase
        .from("reactions")
        .select("id")
        .eq("item_type", "card")
        .eq("item_id", id)
        .eq("reaction_type", "dislike")

      if (dislikeError) {
        console.error("獲取倒讚數時出錯:", dislikeError)
      } else {
        dislikes_count = dislikeData ? dislikeData.length : 0
      }
    } else {
      console.log(`Invalid UUID format: ${id}, using zero counts`)
    }

    // 創建適合 CardDetailContent 組件的卡片數據
    const cardDetailData: CardDetailData = {
      ...card,
      // 確保 topics 是數組
      topics: Array.isArray(card.topics) ? card.topics : [],
      // 確保 subtopics 是數組
      subtopics: Array.isArray(card.subtopics) ? card.subtopics : [],
      // 添加從 reactions 表獲取的讚數和倒讚數
      likes_count,
      dislikes_count,
      stats: {
        likes: likes_count,
        dislikes: dislikes_count,
        comments: 0,
        bookmarks: 0
      },
      comments: []
    }

    // 檢查用戶認證狀態
    const { data } = await supabase.auth.getSession()
    const isAuthenticated = !!data.session

    console.log("Card data with reaction counts:", {
      id: cardDetailData.id,
      likes_count: cardDetailData.likes_count,
      dislikes_count: cardDetailData.dislikes_count,
    })

    return (
      <Suspense fallback={<CardLoading />}>
        <div className="space-y-6">
          {/* Add a key to force client-side re-render when card changes */}
          <CardDetailContent key={`card-${id}`} card={cardDetailData as any} initialAuthState={isAuthenticated} />
        </div>
      </Suspense>
    )
  } catch (error) {
    console.error("Error loading card:", error)
    return <CardError error={error instanceof Error ? error : new Error("未知錯誤")} />
  }
}
