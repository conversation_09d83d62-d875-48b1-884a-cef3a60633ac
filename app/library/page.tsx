"use client"

import { useState, useEffect } from "react"
import { LibrarySidebar } from "@/components/library-sidebar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import { Plus, Search, Loader2, BookmarkIcon, FolderPlus } from "lucide-react"
import { CollectionCard } from "@/components/collection-card"
import { ContentFilter, FilterTags } from "@/components/content-filter"
import { useContentFilter } from "@/hooks/use-content-filter"
import { useToast } from "@/components/ui/use-toast"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { BookmarkedItemCard } from "@/components/bookmarked-item-card"

interface Collection {
  id: string
  name: string
  description: string
  coverImage: string
  itemCount: number
  isPublic: boolean
  categoryId?: string
  categoryName?: string
  createdAt: string
  updatedAt: string
}

interface Category {
  id: string
  name: string
  description?: string
  sortOrder: number
  count: number
  createdAt: string
  updatedAt: string
}

interface BookmarkedItem {
  id: string // 改為 string 以支援 UUID
  type: string
  contentType: "viewpoint" | "thread"
  semanticType?: string
  title: string
  content: string
  author: {
    id: string
    name: string
    avatar: string
  }
  timestamp: string
  topics?: string[]
  subtopics?: string[]
  tags?: string[]
  stats?: {
    likes?: number
    dislikes?: number
    comments?: number
    replies?: number
    views?: number
    bookmarks?: number
  }
  collectionIds: string[] // 改為 string[] 以支援 UUID
  // 觀點卡特有屬性
  contribution_type?: string
  originalAuthor?: string
  originalSource?: string
}

interface LibraryTag {
  id: string
  name: string
  count: number
  type: "topic" | "subtopic"
  originalId: string
}

export default function LibraryPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("collections")
  const [isCreatingCollection, setIsCreatingCollection] = useState(false)
  const [newCollectionName, setNewCollectionName] = useState("")
  const [newCollectionDescription, setNewCollectionDescription] = useState("")
  const [isPrivate, setIsPrivate] = useState(false)
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("")

  // 新增分類相關狀態
  const [isCreatingCategory, setIsCreatingCategory] = useState(false)
  const [newCategoryName, setNewCategoryName] = useState("")
  const [newCategoryDescription, setNewCategoryDescription] = useState("")

  const [activeCategory, setActiveCategory] = useState(undefined as string | undefined)
  const [activeCollection, setActiveCollection] = useState(undefined as string | undefined)
  const [activeTag, setActiveTag] = useState(undefined as string | undefined)
  const [collections, setCollections] = useState<Collection[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [bookmarkedItems, setBookmarkedItems] = useState<BookmarkedItem[]>([])
  const [tags, setTags] = useState<LibraryTag[]>([])

  // 使用新的模組化 filter hook
  const {
    filterState,
    filterActions,
    availableTopics,
    availableSubtopics,
    isLoadingFilters,
    hasActiveFilters,
    applyFiltersToItems
  } = useContentFilter()

  // Loading states
  const [collectionsLoading, setCollectionsLoading] = useState(true)
  const [categoriesLoading, setCategoriesLoading] = useState(true)
  const [bookmarksLoading, setBookmarksLoading] = useState(true)
  const [tagsLoading, setTagsLoading] = useState(true)
  const [creatingCollection, setCreatingCollection] = useState(false)

  // 如果未登入，重定向到登入頁面
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isLoading, isAuthenticated, router])



  // 獲取標籤列表
  const fetchTags = async () => {
    try {
      setTagsLoading(true)
      const response = await fetch("/api/library/tags")
      const result = await response.json()

      if (result.success) {
        setTags(result.data)
      } else {
        console.error("獲取標籤列表失敗:", result.error)
        toast({
          title: "獲取標籤列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取標籤列表時出錯:", error)
      toast({
        title: "獲取標籤列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setTagsLoading(false)
    }
  }

  // 獲取分類列表
  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true)
      console.log("正在獲取分類列表...")
      const response = await fetch("/api/collections/categories")
      const result = await response.json()

      console.log("分類 API 回應:", result)

      if (result.success) {
        console.log("成功獲取分類數據:", result.data)
        setCategories(result.data)
      } else {
        console.error("獲取分類列表失敗:", result.error)
        toast({
          title: "獲取分類列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取分類列表時出錯:", error)
      toast({
        title: "獲取分類列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCategoriesLoading(false)
    }
  }

  // 獲取收藏牆列表
  const fetchCollections = async (categoryId?: string) => {
    try {
      setCollectionsLoading(true)
      let url = "/api/collections"
      if (categoryId) {
        url += `?categoryId=${categoryId}`
      }

      const response = await fetch(url)
      const result = await response.json()

      if (result.success) {
        setCollections(result.data)
      } else {
        console.error("獲取收藏牆列表失敗:", result.error)
        toast({
          title: "獲取收藏牆列表失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取收藏牆列表時出錯:", error)
      toast({
        title: "獲取收藏牆列表失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCollectionsLoading(false)
    }
  }

  // 獲取收藏項目
  const fetchBookmarks = async () => {
    try {
      setBookmarksLoading(true)
      const response = await fetch("/api/bookmarks?limit=100")
      const result = await response.json()

      if (result.success) {
        // 轉換數據格式以匹配現有的 BookmarkedItem 接口
        const formattedItems = result.data.map((bookmark: any) => {
          const content = bookmark.content
          const isCard = bookmark.item_type === "card"

          return {
            id: content.id, // 保持 UUID 格式
            type: isCard ? "viewpoint" : "thread", // 添加 type 屬性
            contentType: isCard ? "viewpoint" : "thread",
            semanticType: content.semantic_type,
            title: content.title,
            content: content.content,
            author: {
              id: content.profiles?.id || "unknown",
              name: content.profiles?.name || "未知用戶",
              avatar: content.profiles?.avatar || "/placeholder.svg",
            },
            timestamp: new Date(content.created_at || content.published_at).toLocaleDateString("zh-TW"),
            topics: content.topics || [], // 使用從 API 返回的主題信息
            subtopics: isCard ? (content.subtopics || []) : undefined,
            tags: !isCard ? (content.topics || []) : undefined, // 討論串使用 topics 作為 tags
            stats: {
              likes: 0, // 暫時為 0，後續可以添加統計信息
              dislikes: isCard ? 0 : undefined,
              comments: isCard ? 0 : undefined,
              replies: !isCard ? 0 : undefined,
            },
            collectionIds: [], // 將在後續獲取真實的收藏牆關聯
            // 觀點卡特有屬性
            ...(isCard && {
              contribution_type: content.contribution_type,
              originalAuthor: content.original_author,
              originalSource: content.original_url,
            }),
          }
        })

        setBookmarkedItems(formattedItems)

        // 獲取每個項目的收藏牆關聯
        await fetchCollectionAssociations(formattedItems)
      } else {
        console.error("獲取收藏項目失敗:", result.error)
        toast({
          title: "獲取收藏項目失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("獲取收藏項目時出錯:", error)
      toast({
        title: "獲取收藏項目失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setBookmarksLoading(false)
    }
  }

  // 獲取收藏項目的收藏牆關聯
  const fetchCollectionAssociations = async (items: BookmarkedItem[]) => {
    try {
      // 為每個項目獲取其收藏牆關聯
      const itemsWithCollections = await Promise.all(
        items.map(async (item) => {
          try {
            const itemType = item.contentType === "viewpoint" ? "card" : "thread"
            const response = await fetch(`/api/collections/items?itemId=${item.id}&itemType=${itemType}`)

            console.log(`獲取項目 ${item.id} 的收藏牆關聯，響應狀態:`, response.status)

            // 檢查響應是否為 JSON
            const contentType = response.headers.get("content-type")
            if (!contentType || !contentType.includes("application/json")) {
              const text = await response.text()
              console.error(`項目 ${item.id} API 返回非 JSON 響應:`, text.substring(0, 200))
              return item
            }

            const result = await response.json()

            if (result.success) {
              return {
                ...item,
                collectionIds: result.data.map((ci: any) => ci.collection_id)
              }
            } else {
              console.warn(`獲取項目 ${item.id} 收藏牆關聯失敗:`, result.error)
              return item
            }
          } catch (error) {
            console.error(`獲取項目 ${item.id} 的收藏牆關聯時出錯:`, error)
            return item
          }
        })
      )

      setBookmarkedItems(itemsWithCollections)
    } catch (error) {
      console.error("獲取收藏牆關聯時出錯:", error)
    }
  }

  // 初始化數據
  useEffect(() => {
    if (isAuthenticated) {
      fetchCategories()
      fetchCollections()
      fetchBookmarks()
      fetchTags()
    }
  }, [isAuthenticated])

  // 當活動分類變更時，重新獲取收藏牆
  useEffect(() => {
    if (isAuthenticated) {
      fetchCollections(activeCategory)
    }
  }, [activeCategory, isAuthenticated])

  // 過濾收藏項目
  const filteredItems = (() => {
    let items = bookmarkedItems

    // 先應用模組化的過濾器
    items = applyFiltersToItems(items)

    // 然後應用標籤過濾
    if (activeTag) {
      items = items.filter((item) => {
        const selectedTag = tags.find(tag => tag.name === activeTag)
        if (selectedTag) {
          const hasTopicMatch = item.topics?.includes(selectedTag.name)
          const hasSubtopicMatch = item.subtopics?.includes(selectedTag.name)
          const hasTagMatch = item.tags?.includes(selectedTag.name)
          return hasTopicMatch || hasSubtopicMatch || hasTagMatch
        } else {
          return item.topics?.includes(activeTag) || item.subtopics?.includes(activeTag) || item.tags?.includes(activeTag)
        }
      })
    }

    // 最後應用搜索過濾
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      items = items.filter((item) =>
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.topics?.some((topic) => topic.toLowerCase().includes(query)) ||
        item.subtopics?.some((subtopic) => subtopic.toLowerCase().includes(query)) ||
        item.tags?.some((tag) => tag.toLowerCase().includes(query))
      )
    }

    return items
  })()



  // 過濾收藏牆 (現在根據真實分類過濾)
  const filteredCollections = collections.filter((collection) => {
    // 根據搜索查詢過濾
    if (searchQuery && !collection.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false
    }
    return true
  })

  // 處理拖放到側邊欄收藏牆
  const handleAddToSidebarCollection = async (itemId: string, itemType: string, collectionId: string) => {
    try {
      // 將前端的 itemType 轉換為 API 期望的格式
      const apiItemType = itemType === "viewpoint" ? "card" : "thread"

      console.log("handleAddToSidebarCollection called with:", { itemId, itemType, apiItemType, collectionId })

      // 調用 API 添加項目到收藏牆
      const response = await fetch("/api/collections/items", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          collectionId: collectionId,
          itemId: itemId, // 已經是 string 格式
          itemType: apiItemType, // 使用轉換後的類型
        }),
      })

      console.log("API 響應狀態:", response.status, response.statusText)

      // 檢查響應是否為 JSON
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text()
        console.error("API 返回非 JSON 響應:", text.substring(0, 200))
        throw new Error("API 返回了無效的響應格式")
      }

      const result = await response.json()
      console.log("API 響應結果:", result)

      if (result.success) {
        // 更新收藏牆的項目數量
        setCollections(prevCollections =>
          prevCollections.map(collection =>
            collection.id === collectionId
              ? { ...collection, itemCount: collection.itemCount + 1 }
              : collection
          )
        )

        // 更新卡片的收藏狀態
        setBookmarkedItems(prevItems =>
          prevItems.map(item =>
            item.id === itemId
              ? { ...item, collectionIds: [...(item.collectionIds || []), collectionId] }
              : item
          )
        )

        toast({
          title: "已添加到收藏牆",
          description: result.message,
        })
      } else {
        if (result.error === "項目已經在收藏牆中") {
          toast({
            title: "項目已在收藏牆中",
            description: result.error,
          })
        } else {
          toast({
            title: "添加失敗",
            description: result.error,
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("添加項目到收藏牆時出錯:", error)
      toast({
        title: "添加失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    }
  }

  // 處理收藏牆切換（從 BookmarkedItemCard 的下拉選單）
  const handleCollectionToggle = async (itemId: string, collectionId: string, isAdd: boolean) => {
    try {
      const itemType = bookmarkedItems.find(item => item.id === itemId)?.contentType === "viewpoint" ? "card" : "thread"

      if (isAdd) {
        // 添加到收藏牆
        const response = await fetch("/api/collections/items", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            collectionId: collectionId,
            itemId: itemId, // 已經是 string 格式
            itemType: itemType,
          }),
        })

        const result = await response.json()

        if (result.success) {
          // 更新本地狀態
          setBookmarkedItems(prevItems =>
            prevItems.map(item =>
              item.id === itemId
                ? { ...item, collectionIds: [...item.collectionIds, collectionId] }
                : item
            )
          )

          // 更新收藏牆的項目數量
          setCollections(prevCollections =>
            prevCollections.map(collection =>
              collection.id === collectionId
                ? { ...collection, itemCount: collection.itemCount + 1 }
                : collection
            )
          )

          toast({
            title: "已添加到收藏牆",
            description: result.message,
          })
        } else {
          throw new Error(result.error)
        }
      } else {
        // 從收藏牆移除
        const response = await fetch("/api/collections/items", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            collectionId: collectionId,
            itemId: itemId, // 已經是 string 格式
            itemType: itemType,
          }),
        })

        const result = await response.json()

        if (result.success) {
          // 更新本地狀態
          setBookmarkedItems(prevItems =>
            prevItems.map(item =>
              item.id === itemId
                ? { ...item, collectionIds: item.collectionIds.filter(id => id !== collectionId) }
                : item
            )
          )

          // 更新收藏牆的項目數量
          setCollections(prevCollections =>
            prevCollections.map(collection =>
              collection.id === collectionId
                ? { ...collection, itemCount: Math.max(0, collection.itemCount - 1) }
                : collection
            )
          )

          toast({
            title: "已從收藏牆移除",
            description: result.message,
          })
        } else {
          throw new Error(result.error)
        }
      }
    } catch (error) {
      console.error("切換收藏牆時出錯:", error)
      throw error // 重新拋出錯誤，讓 BookmarkedItemCard 處理
    }
  }

  // 處理移除收藏
  const handleRemoveBookmark = async (itemId: string) => {
    try {
      const item = bookmarkedItems.find(item => item.id === itemId)
      if (!item) return

      const itemType = item.contentType === "viewpoint" ? "card" : "thread"

      const response = await fetch("/api/bookmarks", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemId: itemId, // 已經是 string 格式
          itemType: itemType,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 從本地狀態中移除項目
        setBookmarkedItems(prevItems => prevItems.filter(item => item.id !== itemId))

        toast({
          title: "已取消收藏",
          description: "項目已從收藏中移除",
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error("移除收藏時出錯:", error)
      throw error // 重新拋出錯誤，讓 BookmarkedItemCard 處理
    }
  }

  // 處理收藏牆分類變更
  const handleMoveCollectionToCategory = async (collectionId: string, categoryId: string | null) => {
    try {
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          categoryId: categoryId,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 更新本地狀態
        setCollections(prevCollections =>
          prevCollections.map(collection =>
            collection.id === collectionId
              ? { ...collection, categoryId: categoryId || undefined }
              : collection
          )
        )

        // 重新獲取分類數據以更新計數
        fetchCategories()

        toast({
          title: "已移動收藏牆",
          description: result.message,
        })
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error("移動收藏牆時出錯:", error)
      toast({
        title: "移動失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
      throw error
    }
  }

  // 處理收藏牆分類變更（從 CollectionCard 的下拉選單）
  const handleCollectionCategoryChange = async (collectionId: string, categoryId: string | null) => {
    await handleMoveCollectionToCategory(collectionId, categoryId)
  }

  // 處理創建新收藏牆
  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      toast({
        title: "請輸入收藏牆名稱",
        variant: "destructive",
      })
      return
    }

    try {
      setCreatingCollection(true)

      const response = await fetch("/api/collections", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCollectionName,
          description: newCollectionDescription,
          isPublic: !isPrivate,
          categoryId: selectedCategoryId || null,
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 添加新收藏牆到列表
        setCollections(prevCollections => [result.data, ...prevCollections])

        setIsCreatingCollection(false)
        setNewCollectionName("")
        setNewCollectionDescription("")
        setIsPrivate(false)
        setSelectedCategoryId("")

        toast({
          title: "收藏牆已創建",
          description: result.message,
        })
      } else {
        toast({
          title: "創建失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("創建收藏牆時出錯:", error)
      toast({
        title: "創建失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCreatingCollection(false)
    }
  }

  // 處理分類點擊
  const handleCategoryClick = (categoryId: string) => {
    if (categoryId === "uncategorized") {
      // 處理未分類的特殊情況
      setActiveCategory(categoryId === activeCategory ? undefined : categoryId)
    } else {
      setActiveCategory(categoryId === activeCategory ? undefined : categoryId)
    }
  }

  // 處理創建新分類
  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: "請輸入分類名稱",
        variant: "destructive",
      })
      return
    }

    try {
      setCategoriesLoading(true)

      // 調用 API 創建分類
      const response = await fetch("/api/collections/categories", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newCategoryName.trim(),
          description: newCategoryDescription.trim(),
        }),
      })

      const result = await response.json()

      if (result.success) {
        // 添加新分類到列表
        setCategories(prevCategories => [result.data, ...prevCategories])

        setIsCreatingCategory(false)
        setNewCategoryName("")
        setNewCategoryDescription("")

        toast({
          title: "分類已創建",
          description: result.message,
        })
      } else {
        toast({
          title: "創建失敗",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("創建分類時出錯:", error)
      toast({
        title: "創建失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setCategoriesLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <>
      <style jsx global>{`
        .dragging-collection .collection-card {
          pointer-events: none;
        }

        .dragging-collection .category-drop-zone {
          background-color: rgba(59, 130, 246, 0.1);
          border: 2px dashed rgba(59, 130, 246, 0.3);
        }

        .dragging-collection .category-drop-zone:hover {
          background-color: rgba(59, 130, 246, 0.2);
          border-color: rgba(59, 130, 246, 0.5);
        }
      `}</style>

      <div className="flex min-h-screen">
        <div className="w-64 min-w-64 border-r">
          <LibrarySidebar
            categories={categories.map(c => ({ id: c.id, name: c.name, count: c.count }))}
            tags={tags.map((t, index) => ({
              id: parseInt(t.id) || (index + 1), // Convert string id to number, fallback to index
              name: t.name,
              count: t.count
            }))}
            collections={collections.map(c => ({
              id: c.id,
              name: c.name,
              itemCount: c.itemCount,
              categoryId: c.categoryId
            }))}
            activeCategory={activeCategory}
            activeCollection={activeCollection}
            activeTag={activeTag}
            onCategoryClick={handleCategoryClick}
            onCollectionClick={(collection) =>
              setActiveCollection(collection === activeCollection ? undefined : collection)
            }
            onTagClick={(tag) => setActiveTag(tag === activeTag ? undefined : tag)}
            onQuickLinkClick={(link) => {
              if (link === "all") {
                setActiveTab("bookmarks")
                setActiveCategory(undefined)
                setActiveTag(undefined)
              } else if (link === "recent") {
                setActiveTab("bookmarks")
                // 這裡可以添加按時間排序的邏輯
              } else if (link === "starred") {
                // 這裡可以添加已標記項目的邏輯
              }
            }}
            onCreateCollection={() => setIsCreatingCollection(true)}
            onCreateCategory={() => setIsCreatingCategory(true)}
            onAddToCollection={handleAddToSidebarCollection}
            onMoveCollectionToCategory={handleMoveCollectionToCategory}
          />
        </div>
        <div className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold">我的收藏庫</h1>
            <div className="flex gap-2">
              <Button onClick={() => setIsCreatingCollection(true)}>
                <FolderPlus className="mr-2 h-4 w-4" />
                創建收藏牆
              </Button>
            </div>
          </div>

          {/* 標籤頁和搜索過濾區域 */}
          <div className="mt-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex items-center justify-between border-b pb-2">
                <TabsList>
                  <TabsTrigger value="collections">收藏牆</TabsTrigger>
                  <TabsTrigger value="bookmarks">所有收藏</TabsTrigger>
                </TabsList>

                <div className="flex items-center gap-2">
                  <div className="relative w-64">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="搜尋收藏..."
                      className="pl-8 h-9"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <ContentFilter
                    filterState={filterState}
                    filterActions={filterActions}
                    availableTopics={availableTopics}
                    availableSubtopics={availableSubtopics}
                    isLoadingFilters={isLoadingFilters}
                  />
                </div>
              </div>

              {/* 標籤和過濾器顯示 */}
              {(activeCategory || activeTag || searchQuery || hasActiveFilters) && (
                <div className="flex flex-wrap items-center gap-2 my-4">
                  {activeCategory && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      分類: {activeCategory === "uncategorized"
                        ? "未分類"
                        : categories.find(c => c.id === activeCategory)?.name || activeCategory}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1"
                        onClick={() => setActiveCategory(undefined)}
                      >
                        ✕
                      </Button>
                    </div>
                  )}
                  {activeTag && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      標籤: #{activeTag}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-1"
                        onClick={() => setActiveTag(undefined)}
                      >
                        ✕
                      </Button>
                    </div>
                  )}
                  {searchQuery && (
                    <div className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      搜尋: {searchQuery}
                      <Button variant="ghost" size="icon" className="h-4 w-4 ml-1" onClick={() => setSearchQuery("")}>
                        ✕
                      </Button>
                    </div>
                  )}

                  {/* 顯示過濾器標籤 */}
                  <FilterTags
                    filterState={filterState}
                    filterActions={filterActions}
                    availableTopics={availableTopics}
                    availableSubtopics={availableSubtopics}
                    showClearAll={false}
                  />

                  {(activeCategory || activeTag || searchQuery || hasActiveFilters) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                      onClick={() => {
                        setActiveCategory(undefined)
                        setActiveTag(undefined)
                        setSearchQuery("")
                        filterActions.clearFilters()
                      }}
                    >
                      清除全部
                    </Button>
                  )}
                </div>
              )}

              <TabsContent value="collections">
                {collectionsLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredCollections.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                    {filteredCollections.map((collection) => {
                      // 找到收藏牆對應的分類名稱
                      const categoryName = collection.categoryId
                        ? categories.find(c => c.id === collection.categoryId)?.name || "未分類"
                        : "未分類"

                      return (
                        <CollectionCard
                          key={collection.id}
                          {...collection}
                          category={categoryName}
                          categories={categories.map(c => ({ id: c.id, name: c.name }))}
                          onCategoryChange={handleCollectionCategoryChange}
                          onCollectionUpdated={() => {
                            // 重新獲取收藏牆和分類資料
                            fetchCollections(activeCategory)
                            fetchCategories()
                          }}
                        />
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto h-12 w-12 text-muted-foreground">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <h3 className="mt-4 text-lg font-medium">尚無收藏牆</h3>
                    <p className="mt-2 text-sm text-muted-foreground">創建您的第一個收藏牆來整理您的收藏。</p>
                    <Button className="mt-4" onClick={() => setIsCreatingCollection(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      創建收藏牆
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="bookmarks">
                {bookmarksLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : filteredItems.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    {filteredItems.map((item) => (
                      <BookmarkedItemCard
                        key={item.id}
                        {...item}
                        collections={collections.map(c => ({ id: c.id, name: c.name, itemCount: c.itemCount }))}
                        onCollectionToggle={handleCollectionToggle}
                        onRemoveBookmark={handleRemoveBookmark}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <BookmarkIcon className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                    <h2 className="mt-4 text-xl font-medium">暫無收藏項目</h2>
                    <p className="mt-2 text-muted-foreground">
                      {searchQuery ? "沒有符合搜尋條件的收藏項目" : "您尚未收藏任何項目"}
                    </p>
                    {!searchQuery && (
                      <Button className="mt-4" asChild>
                        <a href="/">
                          <Plus className="mr-2 h-4 w-4" />
                          瀏覽內容
                        </a>
                      </Button>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* 創建收藏牆對話框 */}
          <Dialog open={isCreatingCollection} onOpenChange={setIsCreatingCollection}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>創建新收藏牆</DialogTitle>
                <DialogDescription>建立一個新的收藏牆來整理您的收藏項目。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    名稱
                  </Label>
                  <Input
                    id="name"
                    value={newCollectionName}
                    onChange={(e) => setNewCollectionName(e.target.value)}
                    className="col-span-3"
                    disabled={creatingCollection}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    描述
                  </Label>
                  <Input
                    id="description"
                    value={newCollectionDescription}
                    onChange={(e) => setNewCollectionDescription(e.target.value)}
                    className="col-span-3"
                    disabled={creatingCollection}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="privacy" className="text-right">
                    隱私設置
                  </Label>
                  <div className="flex items-center space-x-2 col-span-3">
                    <input
                      type="checkbox"
                      id="privacy"
                      checked={isPrivate}
                      onChange={(e) => setIsPrivate(e.target.checked)}
                      className="rounded border-gray-300"
                      disabled={creatingCollection}
                    />
                    <Label htmlFor="privacy">設為私人收藏牆</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateCollection}
                  disabled={creatingCollection}
                >
                  {creatingCollection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  創建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 創建分類對話框 */}
          <Dialog open={isCreatingCategory} onOpenChange={setIsCreatingCategory}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>創建新分類</DialogTitle>
                <DialogDescription>建立一個新的收藏牆分類來整理您的收藏牆。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="categoryName" className="text-right">
                    名稱
                  </Label>
                  <Input
                    id="categoryName"
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                    className="col-span-3"
                    disabled={categoriesLoading}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="categoryDescription" className="text-right">
                    描述
                  </Label>
                  <Input
                    id="categoryDescription"
                    value={newCategoryDescription}
                    onChange={(e) => setNewCategoryDescription(e.target.value)}
                    className="col-span-3"
                    disabled={categoriesLoading}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateCategory}
                  disabled={categoriesLoading}
                >
                  {categoriesLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  創建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  )
}
