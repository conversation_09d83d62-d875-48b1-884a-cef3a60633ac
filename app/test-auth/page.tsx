"use client"

import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useEffect, useState } from "react"

export default function TestAuthPage() {
    const { user, profile, isAuthenticated, isLoading, signOut } = useAuth()
    const router = useRouter()
    const supabase = createClientComponentClient()
    const [sessionInfo, setSessionInfo] = useState<any>(null)
    const [serverSessionInfo, setServerSessionInfo] = useState<any>(null)

    useEffect(() => {
        const checkDirectSession = async () => {
            const { data } = await supabase.auth.getSession()
            setSessionInfo(data.session)
        }

        const checkServerSession = async () => {
            try {
                const response = await fetch('/api/auth/session')
                const data = await response.json()
                setServerSessionInfo(data)
            } catch (error) {
                console.error('Error checking server session:', error)
                setServerSessionInfo({ error: 'Failed to check server session' })
            }
        }

        checkDirectSession()
        checkServerSession()
    }, [supabase])

    if (isLoading) {
        return (
            <div className="container mx-auto p-4">
                <Card>
                    <CardContent className="p-6">
                        <p>載入中...</p>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <Card>
                <CardHeader>
                    <CardTitle>認證狀態測試</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <h3 className="font-semibold mb-2">Context 狀態：</h3>
                        <pre className="bg-gray-100 p-4 rounded overflow-auto">
                            {JSON.stringify(
                                {
                                    isAuthenticated,
                                    user,
                                    profile,
                                    isLoading,
                                },
                                null,
                                2
                            )}
                        </pre>
                    </div>

                    <div>
                        <h3 className="font-semibold mb-2">客戶端 Session 檢查：</h3>
                        <pre className="bg-gray-100 p-4 rounded overflow-auto">
                            {JSON.stringify(
                                {
                                    hasSession: !!sessionInfo,
                                    sessionUser: sessionInfo?.user?.email,
                                    sessionId: sessionInfo?.user?.id,
                                },
                                null,
                                2
                            )}
                        </pre>
                    </div>

                    <div>
                        <h3 className="font-semibold mb-2">服務器端 Session 檢查：</h3>
                        <pre className="bg-gray-100 p-4 rounded overflow-auto">
                            {JSON.stringify(serverSessionInfo, null, 2)}
                        </pre>
                    </div>

                    <div className="flex gap-4">
                        {isAuthenticated ? (
                            <>
                                <Button onClick={() => signOut()}>登出</Button>
                                <Button onClick={() => router.push("/profile")}>前往個人資料</Button>
                                <Button onClick={() => router.push("/my-posts")}>前往我的文章</Button>
                                <Button onClick={() => router.push("/submit")}>前往提交頁面</Button>
                            </>
                        ) : (
                            <Button onClick={() => router.push("/auth/login")}>前往登入</Button>
                        )}
                        <Button onClick={() => window.location.reload()} variant="outline">
                            重新載入頁面
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
} 