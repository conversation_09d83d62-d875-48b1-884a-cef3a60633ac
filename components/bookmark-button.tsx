"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Bookmark, BookmarkCheck } from "lucide-react"
import { useBookmarks } from "@/hooks/use-bookmarks"
import { cn } from "@/lib/utils"

interface BookmarkButtonProps {
    itemId: string
    itemType: "card" | "thread"
    className?: string
    variant?: "default" | "ghost" | "outline" | "secondary"
    size?: "default" | "sm" | "lg" | "icon"
    showCount?: boolean
    disabled?: boolean
}

export function BookmarkButton({
    itemId,
    itemType,
    className,
    variant = "ghost",
    size = "sm",
    showCount = false,
    disabled = false,
}: BookmarkButtonProps) {
    const [isInitialized, setIsInitialized] = useState(false)

    const {
        isBookmarked,
        isLoading,
        count,
        toggleBookmark,
        refreshBookmarkStatus,
        refreshBookmarkCount,
    } = useBookmarks({
        itemId,
        itemType,
    })

    // 組件初始化時獲取收藏狀態和數量
    useEffect(() => {
        const initializeBookmarkData = async () => {
            await Promise.all([
                refreshBookmarkStatus(),
                showCount ? refreshBookmarkCount() : Promise.resolve(),
            ])
            setIsInitialized(true)
        }

        initializeBookmarkData()
    }, [refreshBookmarkStatus, refreshBookmarkCount, showCount])

    const handleClick = async () => {
        if (disabled || isLoading) return
        await toggleBookmark()
    }

    // 在初始化完成前顯示載入狀態
    if (!isInitialized) {
        return (
            <button
                className={cn(
                    "flex items-center gap-1 cursor-pointer transition-colors opacity-50",
                    className
                )}
                disabled
            >
                <Bookmark className="h-3.5 w-3.5" />
                {showCount && <span>-</span>}
            </button>
        )
    }

    return (
        <button
            className={cn(
                "flex items-center gap-1 cursor-pointer transition-colors",
                isBookmarked ? "text-primary" : "hover:text-primary/70",
                className
            )}
            onClick={handleClick}
            disabled={disabled || isLoading}
            aria-label={`${count > 0 ? count : ''} 個收藏`}
            title={isBookmarked ? "取消收藏" : "收藏"}
        >
            <Bookmark className={cn("h-3.5 w-3.5", isBookmarked && "fill-current")} />
            {showCount && count > 0 && (
                <span className="text-xs">
                    {count}
                </span>
            )}
        </button>
    )
} 