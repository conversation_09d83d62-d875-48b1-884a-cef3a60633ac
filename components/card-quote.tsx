"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Wrench, AlertTriangle, FlaskConical, LightbulbIcon, HelpCircle, Sparkles } from "lucide-react"

// 語義類型配置
const semanticTypeConfig: Record<
  string,
  {
    icon: React.ReactNode
    label: string
    description: string
    color: string
  }
> = {
  concept: {
    icon: <BookOpen className="h-3.5 w-3.5" />,
    label: "概念整理",
    description: "原理、理論、詞彙解釋、系統性知識輸出",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
  implementation: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "實作",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  warning: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  experience: {
    icon: <FlaskConical className="h-3.5 w-3.5" />,
    label: "實測經驗",
    description: "自己做過的實驗結果、性能比較、效果說明",
    color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
  },
  insight: {
    icon: <LightbulbIcon className="h-3.5 w-3.5" />,
    label: "看法",
    description: "對該主題的觀點、價值判斷、立場",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  debate: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "爭議論點",
    description: "值不值得做？哪個方法比較爛？",
    color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
  },
  guide: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "工具教學",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  trap: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
}

interface CardQuoteProps {
  card: {
    id: number
    title: string
    content: string
    author: string
    type: string
    tags: string[]
    isLeader?: boolean
    topics?: Array<{ id: string | number; name: string }>
  }
  className?: string
}

export function CardQuote({ card, className }: CardQuoteProps) {
  const typeConfig = semanticTypeConfig[card.type as string] || semanticTypeConfig.concept

  // 截斷內容
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  return (
    <div className={cn("border rounded-md overflow-hidden bg-card", className)}>
      <div className={`p-3 border-l-4 ${typeConfig.color.replace("bg-", "border-")}`}>
        <div className="flex flex-col gap-1">
          <div className="flex flex-wrap items-center gap-2">
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主題標籤 */}
            {card.topics && card.topics.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                <span>{card.topics[0].name}</span>
              </Badge>
            )}

            {card.tags && card.tags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{card.tags[0]}</span>
              </Badge>
            )}
          </div>
          <div className="text-xs text-muted-foreground">作者：{card.author}</div>
          <h4 className="text-sm font-bold">
            <a href={`/card/${card.id}`} className="text-primary hover:underline">
              {card.title}
            </a>
          </h4>
          <div className="text-xs text-muted-foreground line-clamp-2">{truncateText(card.content, 150)}</div>
        </div>
      </div>
    </div>
  )
}
