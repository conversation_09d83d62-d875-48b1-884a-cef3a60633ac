"use client"

import { useEditor, EditorContent } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Link from "@tiptap/extension-link"
import Image from "@tiptap/extension-image"
import { CardExtension } from "@/components/tiptap-card-extension"
import { cn } from "@/lib/utils"

interface RichTextContentProps {
  content: string
  className?: string
}

export function RichTextContent({ content, className }: RichTextContentProps) {
  // 添加调试信息
  console.log("RichTextContent received:", {
    contentLength: content ? content.length : 0,
    contentSample: content ? content.substring(0, 50) : null,
    isNull: content === null,
    isUndefined: content === undefined,
    isEmptyString: content === "",
  })

  // 确保内容是字符串
  const safeContent = typeof content === "string" ? content : ""

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          class: "text-primary underline underline-offset-2",
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: "rounded-md max-w-full my-4",
        },
      }),
      CardExtension,
    ],
    content: safeContent,
    editable: false,
    editorProps: {
      attributes: {
        class: cn("prose prose-sm max-w-none focus:outline-none", className),
      },
    },
    immediatelyRender: false,
  })

  if (!editor) {
    return <div className={className}>加载中...</div>
  }

  // 如果内容为空，显示占位符
  if (!safeContent.trim()) {
    return <div className={cn("text-muted-foreground italic", className)}>无内容</div>
  }

  return <EditorContent editor={editor} className={className} />
}
