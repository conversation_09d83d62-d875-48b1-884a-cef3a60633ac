"use client"

import type React from "react"

import { Node, mergeAttributes } from "@tiptap/core"
import { ReactNodeViewRenderer, NodeViewWrapper } from "@tiptap/react"
import { Badge } from "@/components/ui/badge"
import {
  BookOpen,
  Wrench,
  AlertTriangle,
  FlaskConical,
  LightbulbIcon,
  HelpCircle,
  FileText,
  MessageSquare,
} from "lucide-react"
import { cn } from "@/lib/utils"

// 定义内容类型
type ContentType = "viewpoint" | "thread"

// 内容类型配置
const contentTypeConfig: Record<
  ContentType,
  {
    icon: React.ReactNode
    label: string
    color: string
  }
> = {
  viewpoint: {
    icon: <FileText className="h-3.5 w-3.5" />,
    label: "觀點卡",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  },
  thread: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論串",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  },
}

// 語義類型配置
const semanticTypeConfig: Record<
  string,
  {
    icon: React.ReactNode
    label: string
    description: string
    color: string
  }
> = {
  concept: {
    icon: <BookOpen className="h-3.5 w-3.5" />,
    label: "概念整理",
    description: "原理、理論、詞彙解釋、系統性知識輸出",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
  implementation: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "實作",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  warning: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  experience: {
    icon: <FlaskConical className="h-3.5 w-3.5" />,
    label: "實測經驗",
    description: "自己做過的實驗結果、性能比較、效果說明",
    color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
  },
  insight: {
    icon: <LightbulbIcon className="h-3.5 w-3.5" />,
    label: "看法",
    description: "對該主題的觀點、價值判斷、立場",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  debate: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "爭議論點",
    description: "值不值得做？哪個方法比較爛？",
    color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
  },
  discussion: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論",
    description: "一般討論主題",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  question: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "問題",
    description: "尋求解答的問題",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
}

// 卡片來源類型配置
const sourceTypeConfig: Record<
  string,
  {
    label: string
    badge: string
    color: string
    show: boolean
  }
> = {
  leader: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
  community: {
    label: "社群貢獻",
    badge: "Community",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    show: true,
  },
}

// 卡片引用组件
const CardQuoteComponent = (props: any) => {
  const {
    node: {
      attrs: { cardId, cardTitle, cardContent, cardAuthor, cardType, cardTags, isLeader, contentType = "viewpoint" },
    },
    updateAttributes,
    deleteNode,
  } = props

  const typeConfig = semanticTypeConfig[cardType] || semanticTypeConfig.concept
  const sourceConfig = sourceTypeConfig[isLeader ? "leader" : "community"]
  const cTypeConfig = contentTypeConfig[contentType as ContentType] || contentTypeConfig.viewpoint

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  return (
    <NodeViewWrapper className="card-quote-wrapper">
      <div className="not-prose my-4 p-3 border rounded-md bg-muted/20 hover:bg-muted/30 transition-colors">
        <div className="flex flex-col gap-2">
          {/* 卡片头部 - 类型徽章和标签 */}
          <div className="flex flex-wrap items-center gap-2">
            {/* 内容类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", cTypeConfig.color)}>
              {cTypeConfig.icon}
              <span>{cTypeConfig.label}</span>
            </Badge>

            {/* 语义类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主题徽章 */}
            {cardTags && cardTags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{cardTags[0]}</span>
              </Badge>
            )}

            {/* 来源类型徽章 */}
            {isLeader && sourceConfig.show && (
              <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                <span>{sourceConfig.badge}</span>
              </Badge>
            )}
          </div>

          {/* 作者信息 */}
          <div className="text-xs text-muted-foreground">作者：{cardAuthor}</div>

          {/* 标题 */}
          <h4 className="text-sm font-bold text-foreground">{cardTitle}</h4>

          {/* 内容 */}
          <div className="text-xs text-muted-foreground">{truncateText(cardContent, 120)}</div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 mt-1">
            {cardTags &&
              cardTags.map((tag: string, tagIndex: number) => (
                <Badge key={tagIndex} variant="outline" className="text-xs py-0">
                  #{tag}
                </Badge>
              ))}
          </div>

          {/* 底部链接 */}
          <div className="text-xs text-primary mt-1">
            <a href={contentType === "viewpoint" ? `/card/${cardId}` : `/thread/${cardId}`} className="hover:underline">
              查看完整{contentType === "viewpoint" ? "卡片" : "討論"}
            </a>
          </div>
        </div>
      </div>
    </NodeViewWrapper>
  )
}

// TipTap 卡片引用扩展
export const CardExtension = Node.create({
  name: "cardQuote",
  group: "block",
  atom: true,

  addAttributes() {
    return {
      cardId: {
        default: null,
      },
      cardTitle: {
        default: "",
      },
      cardContent: {
        default: "",
      },
      cardAuthor: {
        default: "",
      },
      cardType: {
        default: "concept",
      },
      cardTags: {
        default: [],
      },
      isLeader: {
        default: false,
      },
      contentType: {
        default: "viewpoint",
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="card-quote"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ["div", mergeAttributes({ "data-type": "card-quote" }, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CardQuoteComponent)
  },
})
