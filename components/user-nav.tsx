"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import { BookMarked, LogIn, LogOut, Settings, User } from "lucide-react"
import Link from "next/link"
import { useSidebar } from "@/components/ui/sidebar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ThemeToggle } from "@/components/theme-toggle"

interface UserNavProps {
  variant?: "sidebar" | "header"
}

export function UserNav({ variant = "header" }: UserNavProps) {
  const { user, profile, isAuthenticated, signOut } = useAuth()
  const { collapsed } = useSidebar()
  const isSidebar = variant === "sidebar"
  const isCollapsed = isSidebar && collapsed

  // 未登入狀態
  if (!isAuthenticated || !user || !profile) {
    // 側邊欄折疊狀態下的登入按鈕和主題切換
    if (isCollapsed) {
      return (
        <div className="flex flex-col items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full" asChild>
                  <Link href="/auth/login">
                    <LogIn className="h-4 w-4" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right">登入</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <ThemeToggle showTooltip={true} tooltipSide="right" variant="ghost" />
        </div>
      )
    }

    // 正常顯示的登入按鈕和主題切換
    return (
      <div className={`flex items-center gap-2 ${isSidebar ? "flex-col w-full" : ""}`}>
        <Button
          variant={isSidebar ? "outline" : "ghost"}
          size="sm"
          className={isSidebar ? "w-full justify-start" : ""}
          asChild
        >
          <Link href="/auth/login" className="flex items-center">
            <LogIn className="h-4 w-4 mr-2" />
            <span>登入</span>
          </Link>
        </Button>

        {isSidebar ? <ThemeToggle variant="pill" compact={true} /> : <ThemeToggle variant="ghost" showLabel={true} />}
      </div>
    )
  }

  // 已登入狀態 - 折疊時
  if (isCollapsed) {
    return (
      <DropdownMenu>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 rounded-full p-0">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={profile.avatar_url || `/placeholder.svg?height=32&width=32&query=${profile.name}`}
                      alt={profile.name}
                    />
                    <AvatarFallback>{profile.name.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent side="right">{profile.name}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <DropdownMenuContent className="w-56 z-50" align="end" side="right" sideOffset={8} forceMount>
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">{profile.name}</p>
              <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                <span>個人資料</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/library" className="flex items-center">
                <BookMarked className="mr-2 h-4 w-4" />
                <span>我的收藏</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="flex items-center">
                <Settings className="mr-2 h-4 w-4" />
                <span>設定</span>
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => signOut()} className="flex items-center">
            <LogOut className="mr-2 h-4 w-4" />
            <span>登出</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <div className="w-full px-2 py-1.5">
              <ThemeToggle variant="pill" showLabel={true} />
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // 已登入狀態 - 正常顯示
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`relative rounded-full ${isSidebar ? "w-full justify-start" : "h-8 w-8 p-0"}`}
        >
          {isSidebar ? (
            <div className="flex items-center">
              <Avatar className="h-6 w-6 mr-2">
                <AvatarImage
                  src={profile.avatar_url || `/placeholder.svg?height=32&width=32&query=${profile.name}`}
                  alt={profile.name}
                />
                <AvatarFallback>{profile.name.substring(0, 2)}</AvatarFallback>
              </Avatar>
              <span className="text-sm">{profile.name}</span>
            </div>
          ) : (
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={profile.avatar_url || `/placeholder.svg?height=32&width=32&query=${profile.name}`}
                alt={profile.name}
              />
              <AvatarFallback>{profile.name.substring(0, 2)}</AvatarFallback>
            </Avatar>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-56 z-50"
        align={isSidebar ? "center" : "end"}
        side={isSidebar ? "right" : "bottom"}
        sideOffset={isSidebar ? 8 : 4}
        forceMount
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{profile.name}</p>
            <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/profile" className="flex items-center">
              <User className="mr-2 h-4 w-4" />
              <span>個人資料</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/library" className="flex items-center">
              <BookMarked className="mr-2 h-4 w-4" />
              <span>我的收藏</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/settings" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              <span>設定</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => signOut()} className="flex items-center">
          <LogOut className="mr-2 h-4 w-4" />
          <span>登出</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <div className="w-full px-2 py-1.5">
            <ThemeToggle variant="pill" showLabel={true} />
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
