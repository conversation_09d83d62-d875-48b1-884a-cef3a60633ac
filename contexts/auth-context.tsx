"use client"

import type React from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import { createContext, useContext, useEffect, useState, useMemo } from "react"

type Profile = {
  id: string
  name: string
  email: string
  avatar?: string | null
  bio?: string
  created_at?: string
  updated_at?: string
}

type User = {
  id: string
  email: string
}

type AuthContextType = {
  user: User | null
  profile: Profile | null
  isAuthenticated: boolean
  isLoading: boolean
  signUp: (email: string, password: string, name: string) => Promise<{ error: Error | null }>
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>
  signInWithProvider: (provider: "google" | "facebook" | "github") => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  updateProfile: (data: Partial<Profile>) => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  signUp: async () => ({ error: null }),
  signIn: async () => ({ error: null }),
  signInWithProvider: async () => { },
  signOut: async () => { },
  refreshProfile: async () => { },
  updateProfile: async () => { },
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // 創建 Supabase 客戶端
  const supabase = useMemo(() => {
    console.log("Creating Supabase client with env vars:", {
      hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    })
    return createBrowserClient()
  }, [])

  // 獲取用戶資料
  const fetchProfile = async (userId: string) => {
    try {
      console.log("Fetching profile for user:", userId)

      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single()

      if (error) {
        console.error("Error fetching profile:", error)

        // 如果找不到資料，嘗試創建
        const { data: userData } = await supabase.auth.getUser()
        if (userData?.user) {
          const { data: newProfile, error: insertError } = await supabase
            .from("profiles")
            .insert({
              id: userId,
              name: userData.user.user_metadata?.name || userData.user.email?.split('@')[0] || "用戶",
              email: userData.user.email || "",
            })
            .select()
            .single()

          if (!insertError && newProfile) {
            setProfile(newProfile as Profile)
          }
        }
      } else if (data) {
        console.log("Profile found:", data)
        setProfile(data as Profile)
      }
    } catch (error) {
      console.error("Error in fetchProfile:", error)
    }
  }

  // 初始化認證狀態
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true)

        // 獲取當前用戶
        const { data: { user }, error } = await supabase.auth.getUser()

        if (user && !error) {
          const currentUser = {
            id: user.id,
            email: user.email || "",
          }
          setUser(currentUser)
          setIsAuthenticated(true)

          // 獲取用戶資料
          await fetchProfile(user.id)
        } else {
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
        }
      } catch (error) {
        console.error("Error initializing auth:", error)
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()

    // 監聽認證狀態變化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log("Auth state changed:", event)

        if (event === 'SIGNED_IN' && session) {
          const currentUser = {
            id: session.user.id,
            email: session.user.email || "",
          }
          setUser(currentUser)
          setIsAuthenticated(true)
          await fetchProfile(session.user.id)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
        } else if (event === 'TOKEN_REFRESHED' && session) {
          // Token 刷新時，保持用戶狀態
          const currentUser = {
            id: session.user.id,
            email: session.user.email || "",
          }
          setUser(currentUser)
          setIsAuthenticated(true)
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  // 註冊
  const signUp = async (email: string, password: string, name: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)

        // 創建用戶資料
        await supabase
          .from("profiles")
          .insert({
            id: data.user.id,
            name,
            email: data.user.email || "",
          })
      }

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }

  // 登入
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { error }
      }

      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)
        await fetchProfile(data.user.id)
      }

      return { error: null }
    } catch (error) {
      return { error: error as Error }
    }
  }

  // OAuth 登入
  const signInWithProvider = async (provider: "google" | "facebook" | "github") => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        console.error(`Error signing in with ${provider}:`, error)
      }
    } catch (error) {
      console.error(`Error in signInWithProvider (${provider}):`, error)
    }
  }

  // 登出
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()

      if (error) {
        throw error
      }

      setUser(null)
      setProfile(null)
      setIsAuthenticated(false)
      router.push("/")
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  // 刷新用戶資料
  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id)
    }
  }

  // 更新用戶資料
  const updateProfile = async (data: Partial<Profile>) => {
    if (!user) {
      throw new Error("User not authenticated")
    }

    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id)

      if (error) {
        throw error
      }

      await refreshProfile()
    } catch (error) {
      console.error("Error updating profile:", error)
      throw error
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isAuthenticated,
        isLoading,
        signUp,
        signIn,
        signInWithProvider,
        signOut,
        refreshProfile,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
