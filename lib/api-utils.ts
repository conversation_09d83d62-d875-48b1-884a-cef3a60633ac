import { createBrowserClient } from "@/lib/supabase/client"

// 檢查是否為客戶端環境
export function isClient() {
  return typeof window !== "undefined"
}

// 獲取客戶端 Supabase 客戶端
export function getClientSupabase() {
  return createBrowserClient()
}

// 客戶端版本的 Supabase 獲取函數
export function getSupabase() {
  return createBrowserClient()
}

// API 響應類型
export type ApiResponse<T> = {
  success: boolean
  data: T | null
  error?: string
  message?: string
  count?: number
}

// 分頁參數類型
export interface PaginationParams {
  page?: number
  limit?: number
}

// 計算分頁偏移量
export function calculateOffset(page: number, limit: number) {
  return (page - 1) * limit
}

// 創建成功響應
export function successResponse<T>(data: T, count?: number, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
    count,
  }
}

// 創建錯誤響應
export function errorResponse<T>(error: string, status = 400): ApiResponse<T> {
  return {
    success: false,
    data: null,
    error,
  }
}

// 處理錯誤
export function handleError(error: any): ApiResponse<null> {
  console.error("API Error:", error)

  // 處理 Supabase 錯誤
  if (error?.code === "429" || (error?.message && error.message.includes("Too Many Requests"))) {
    return errorResponse("請求過於頻繁，請稍後再試", 429)
  }

  // 處理 JSON 解析錯誤
  if (error instanceof SyntaxError && error.message.includes("Unexpected token")) {
    return errorResponse("無效的響應格式，請稍後再試", 400)
  }

  // 處理網絡錯誤
  if (error?.code === "ECONNREFUSED" || error?.code === "ETIMEDOUT") {
    return errorResponse("無法連接到服務器，請檢查您的網絡連接", 503)
  }

  // 處理認證錯誤
  if (error?.code === "PGRST301" || error?.message?.includes("JWT")) {
    return errorResponse("認證已過期，請重新登入", 401)
  }

  return {
    success: false,
    data: null,
    error: error.message || "發生未知錯誤",
  }
}
