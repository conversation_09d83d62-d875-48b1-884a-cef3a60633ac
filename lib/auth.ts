// 用戶類型定義
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  bio?: string
  role: "user" | "admin"
  createdAt: string
}

// JWT 令牌解碼後的類型
interface JwtPayload {
  sub: string
  name: string
  email: string
  role: "user" | "admin"
  iat: number
  exp: number
}

// 模擬用戶數據 - 實際應用中應從數據庫獲取
const USERS: Record<string, User> = {
  "1": {
    id: "1",
    name: "技術愛好者",
    email: "<EMAIL>",
    avatar: "/mystical-forest-spirit.png",
    bio: "AI 技術愛好者，專注於 LLM 和 RAG 系統的研究與應用。",
    role: "user",
    createdAt: "2023-01-15",
  },
  "2": {
    id: "2",
    name: "AI研究員",
    email: "<EMAIL>",
    avatar: "/diverse-research-team.png",
    bio: "AI 研究員，專注於多模態模型和智能體系統的研究。",
    role: "user",
    createdAt: "2023-02-20",
  },
  "3": {
    id: "3",
    name: "系統架構師",
    email: "<EMAIL>",
    avatar: "/modern-architect-studio.png",
    bio: "系統架構師，專注於大規模 AI 系統的設計與實現。",
    role: "admin",
    createdAt: "2023-03-10",
  },
}

// 模擬用戶憑證 - 實際應用中應從數據庫獲取並加密存儲
const USER_CREDENTIALS: Record<string, { password: string; userId: string }> = {
  "<EMAIL>": { password: "password123", userId: "1" },
  "<EMAIL>": { password: "password123", userId: "2" },
  "<EMAIL>": { password: "password123", userId: "3" },
}

// 模擬 JWT 密鑰 - 實際應用中應使用環境變量
const JWT_SECRET = "your-jwt-secret"

// 生成 JWT 令牌
export function generateToken(user: User): string {
  // 實際應用中應使用 jsonwebtoken 庫
  // 這裡我們模擬一個 JWT 令牌
  const payload = {
    sub: user.id,
    name: user.name,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24, // 24 小時過期
  }

  // 模擬 JWT 令牌
  return btoa(JSON.stringify(payload))
}

// 驗證 JWT 令牌
export function verifyToken(token: string): JwtPayload | null {
  try {
    // 實際應用中應使用 jsonwebtoken 庫
    // 這裡我們模擬 JWT 驗證
    const payload = JSON.parse(atob(token)) as JwtPayload

    // 檢查令牌是否過期
    if (payload.exp < Math.floor(Date.now() / 1000)) {
      return null
    }

    return payload
  } catch (error) {
    return null
  }
}

// 模擬登錄
export async function login(email: string, password: string): Promise<{ user: User; token: string } | null> {
  // 模擬 API 請求延遲
  await new Promise((resolve) => setTimeout(resolve, 500))

  const credentials = USER_CREDENTIALS[email]

  if (!credentials || credentials.password !== password) {
    return null
  }

  const user = USERS[credentials.userId]
  const token = generateToken(user)

  return { user, token }
}

// 模擬註冊
export async function register(
  name: string,
  email: string,
  password: string,
): Promise<{ user: User; token: string } | null> {
  // 模擬 API 請求延遲
  await new Promise((resolve) => setTimeout(resolve, 500))

  // 檢查郵箱是否已被使用
  if (USER_CREDENTIALS[email]) {
    return null
  }

  // 生成新用戶 ID
  const userId = String(Object.keys(USERS).length + 1)

  // 創建新用戶
  const user: User = {
    id: userId,
    name,
    email,
    role: "user",
    createdAt: new Date().toISOString(),
  }

  // 在實際應用中，這裡應該將用戶保存到數據庫
  USERS[userId] = user
  USER_CREDENTIALS[email] = { password, userId }

  const token = generateToken(user)

  return { user, token }
}

// 根據 ID 獲取用戶
export async function getUserById(id: string): Promise<User | null> {
  // 模擬 API 請求延遲
  await new Promise((resolve) => setTimeout(resolve, 300))

  return USERS[id] || null
}

// 更新用戶資料
export async function updateUserProfile(id: string, data: Partial<User>): Promise<User | null> {
  // 模擬 API 請求延遲
  await new Promise((resolve) => setTimeout(resolve, 500))

  const user = USERS[id]

  if (!user) {
    return null
  }

  // 更新用戶資料
  const updatedUser = {
    ...user,
    ...data,
    id: user.id, // 確保 ID 不被修改
    email: user.email, // 確保郵箱不被修改
    role: user.role, // 確保角色不被修改
    createdAt: user.createdAt, // 確保創建時間不被修改
  }

  // 在實際應用中，這裡應該將更新後的用戶保存到數據庫
  USERS[id] = updatedUser

  return updatedUser
}

// 從 localStorage 獲取令牌
export function getStoredToken(): string | null {
  if (typeof window !== "undefined") {
    return localStorage.getItem("auth_token")
  }
  return null
}

// 將令牌存儲到 localStorage
export function storeToken(token: string): void {
  if (typeof window !== "undefined") {
    localStorage.setItem("auth_token", token)
  }
}

// 從 localStorage 移除令牌
export function removeStoredToken(): void {
  if (typeof window !== "undefined") {
    localStorage.removeItem("auth_token")
  }
}

// 從令牌獲取用戶信息
export async function getUserFromToken(token: string): Promise<User | null> {
  const payload = verifyToken(token)

  if (!payload) {
    return null
  }

  return getUserById(payload.sub)
}
