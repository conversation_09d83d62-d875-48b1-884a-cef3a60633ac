import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"

// 添加評論
export async function addComment({
  itemType,
  itemId,
  content,
  parentCommentId = null,
  referencedCard = null,
}: {
  itemType: "card" | "thread"
  itemId: number | string
  content: string
  parentCommentId?: string | null
  referencedCard?: { id: number | string; type: string } | null
}) {
  try {
    const supabase = createClientComponentClient()

    // 獲取當前用戶
    const { data: userData, error: userError } = await supabase.auth.getUser()
    if (userError || !userData.user) {
      console.error("Error getting user:", userError)
      return { success: false, error: "未登入或獲取用戶信息失敗" }
    }

    const userId = userData.user.id

    // 創建評論 - 使用正確的列名
    const { data: commentData, error: commentError } = await supabase
      .from("comments")
      .insert({
        root_item_type: itemType,
        root_item_id: itemId,
        content: content,
        author_id: userId,
        parent_comment_id: parentCommentId,
      })
      .select("id, created_at")
      .single()

    if (commentError) {
      console.error("Error creating comment:", commentError)
      return { success: false, error: "創建評論失敗: " + commentError.message }
    }

    // 如果有引用的卡片，添加到 CONTENT_REFERENCES 表
    if (referencedCard && commentData) {
      const { error: refError } = await supabase.from("content_references").insert({
        source_type: "comment",
        source_id: commentData.id,
        target_type: "card",
        target_id: referencedCard.id,
        reference_type: referencedCard.type || "quote",
        created_at: new Date().toISOString(),
      })

      if (refError) {
        console.error("Error creating content reference:", refError)
        // 不要因為引用創建失敗而阻止評論創建
        // 但是記錄錯誤
      }
    }

    // 獲取用戶信息
    const { data: rawProfileData, error: profileError } = await supabase
      .from("profiles")
      .select("id, name, avatar, bio, email, created_at")
      .eq("id", userId)
      .single()

    if (profileError) {
      console.error("Error getting profile:", profileError)
      return { success: false, error: "獲取用戶信息失敗" }
    }
    const profileData = rawProfileData;

    // 如果有引用的卡片，獲取卡片信息用於返回
    let referencedCardInfo = null
    if (referencedCard) {
      const { data: cardResult, error: cardError } = await supabase
        .from("cards")
        .select(`
          id,
          title,
          content,
          semantic_type,
          author:profiles!author_id(id, name, avatar, bio, email, created_at), 
          card_topics(topics(id, name, slug)), 
          card_subtopics(subtopics(id, name))
        `)
        .eq("id", referencedCard.id)
        .single()

      if (cardError) {
        console.error("Error getting referenced card:", cardError)
      } else if (cardResult && !Array.isArray(cardResult)) {
        const cardAuthor = Array.isArray(cardResult.author) ? cardResult.author[0] : cardResult.author;
        referencedCardInfo = {
          id: cardResult.id,
          title: cardResult.title,
          content: cardResult.content,
          semantic_type: cardResult.semantic_type,
          author_name: cardAuthor?.name || "未知作者",
          tags: cardResult.card_subtopics?.map((cs: any) => cs.subtopics?.name).filter(Boolean) || [],
          topics: cardResult.card_topics?.map((ct: any) => ct.topics).filter(Boolean) || [],
          reference_type: referencedCard.type || "quote",
        }
      }
    }

    // 返回評論數據
    return {
      success: true,
      data: {
        id: commentData.id,
        content,
        created_at: commentData.created_at,
        parent_comment_id: parentCommentId,
        author: {
          name: profileData?.name || "未知用戶",
          avatar: profileData?.avatar || undefined,
        },
        referenced_card: referencedCardInfo,
      },
    }
  } catch (error) {
    console.error("Error in addComment:", error)
    return { success: false, error: "添加評論時發生錯誤" }
  }
}

// 獲取評論的引用卡片
export async function getCommentReferences(commentIds: string[] | number[]) {
  if (!commentIds.length) return { success: true, data: {} }

  try {
    const supabase = createClientComponentClient()

    // 獲取所有評論的引用
    const { data: references, error: refError } = await supabase
      .from("content_references")
      .select("*")
      .eq("source_type", "comment")
      .in("source_id", commentIds)
      .eq("target_type", "card")

    if (refError) {
      console.error("Error fetching comment references:", refError)
      return { success: false, error: "獲取評論引用失敗" }
    }

    // 如果沒有引用，返回空對象
    if (!references || references.length === 0) {
      return { success: true, data: {} }
    }

    // 獲取所有引用的卡片 ID
    const cardIds = references.map((ref) => ref.target_id)

    // 獲取卡片詳情
    const { data: cards, error: cardError } = await supabase
      .from("cards")
      .select(`
        id, 
        title, 
        content, 
        semantic_type, 
        topics (id, name, slug),
        subtopics (id, name),
        author:profiles!author_id (id, name, avatar, bio, email, created_at)
      `)
      .in("id", cardIds)

    if (cardError) {
      console.error("Error fetching referenced cards:", cardError)
      return { success: false, error: "獲取引用卡片失敗" }
    }

    // 構建評論 ID 到引用卡片的映射
    const commentReferences: Record<string, any> = {}

    references.forEach((ref) => {
      const card = cards.find((c) => c.id === ref.target_id)
      if (card) {
        const cardAuthorProfile = Array.isArray(card.author) ? card.author[0] : card.author;
        const cardData = {
          id: card.id,
          title: card.title,
          content: card.content,
          semantic_type: card.semantic_type,
          author_name: cardAuthorProfile?.name || "未知作者",
          tags: card.subtopics ? card.subtopics.map((s: any) => s.name) : [],
          topics: card.topics || [],
          reference_type: ref.reference_type,
        }

        commentReferences[ref.source_id] = cardData
      }
    })

    return { success: true, data: commentReferences }
  } catch (error) {
    console.error("Error in getCommentReferences:", error)
    return { success: false, error: "獲取評論引用時發生錯誤" }
  }
}
