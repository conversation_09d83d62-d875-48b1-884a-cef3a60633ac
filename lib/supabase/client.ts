import { createClient } from "@supabase/supabase-js"

// 使用單例模式確保只創建一個客戶端實例
let supabaseClient: ReturnType<typeof createClient> | null = null

// Cookie 操作輔助函數
function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return decodeURIComponent(parts.pop()?.split(';').shift() || '')
  }
  return null
}

function setCookie(name: string, value: string, options: any = {}) {
  if (typeof window === 'undefined') return

  const encodedValue = encodeURIComponent(value)
  let cookieString = `${name}=${encodedValue}`

  cookieString += '; path=/'
  cookieString += '; max-age=2592000' // 30 days
  cookieString += '; samesite=lax'

  if (options.secure || (typeof window !== 'undefined' && window.location.protocol === 'https:')) {
    cookieString += '; secure'
  }

  document.cookie = cookieString
}

function removeCookie(name: string) {
  if (typeof window === 'undefined') return
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`
}

// 保持原來的函數名稱 createBrowserClient
export function createBrowserClient() {
  if (supabaseClient) return supabaseClient

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error(
      "Missing Supabase environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.",
    )
    // 提供一個默認值，避免運行時錯誤
    return createClient("https://your-project.supabase.co", "your-anon-key")
  }

  supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storage: {
        getItem: (key) => {
          if (typeof window === 'undefined') return null

          // 檢查是否是 Supabase auth key
          if (key.includes('auth-token')) {
            // 對於認證 token，優先從 cookie 讀取
            const cookieValue = getCookie(key)
            if (cookieValue) {
              return cookieValue
            }
          }

          // 回退到 localStorage
          return window.localStorage.getItem(key)
        },
        setItem: (key, value) => {
          if (typeof window === 'undefined') return

          // 檢查是否是 Supabase auth key
          if (key.includes('auth-token')) {
            // 清除舊的通用 cookie（如果存在）
            if (key.includes('-auth-token') && !key.startsWith('sb-auth-token-')) {
              removeCookie('sb-auth-token')
            }

            // 對於認證 token，同時設置 cookie 和 localStorage
            setCookie(key, value, {
              secure: window.location.protocol === 'https:'
            })
          }

          // 總是設置 localStorage 作為備份
          window.localStorage.setItem(key, value)
        },
        removeItem: (key) => {
          if (typeof window === 'undefined') return

          // 檢查是否是 Supabase auth key
          if (key.includes('auth-token')) {
            // 清除 cookie
            removeCookie(key)
          }

          // 清除 localStorage
          window.localStorage.removeItem(key)
        },
      },
      // 使用項目特定的 storageKey
      storageKey: `sb-${supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1] || 'default'}-auth-token`,
    },
    global: {
      headers: {
        'X-Client-Info': 'nextjs-app'
      }
    }
  })

  return supabaseClient
}

// 為了向後兼容，保留 supabase 實例
export const supabase = createBrowserClient()
