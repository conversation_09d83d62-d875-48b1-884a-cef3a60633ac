import { createServerClient as createSupabaseServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"

export function createServerClient(cookieStore?: ReturnType<typeof cookies>) {
  const cookieStoreToUse = cookieStore || cookies()
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase environment variables")
  }

  return createSupabaseServerClient(
    supabaseUrl,
    supabaseKey,
    {
      cookies: {
        getAll: async () => {
          const resolvedCookies = await cookieStoreToUse
          return resolvedCookies.getAll() || []
        },
        setAll: async (cookiesToSet) => {
          const resolvedCookies = await cookieStoreToUse
          cookiesToSet.forEach(({ name, value, options }) => {
            resolvedCookies.set(name, value, options)
          })
        }
      },
    }
  )
}
