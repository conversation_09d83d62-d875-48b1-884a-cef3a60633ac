import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'

export async function middleware(request: NextRequest) {
    const response = NextResponse.next()
    const { pathname } = request.nextUrl

    // 不需要驗證的路徑
    const publicPaths = [
        '/',
        '/auth/login',
        '/auth/register',
        '/auth/callback',
        '/api/auth/callback',
        '/explore',
        '/topic',
        '/thread',
        '/about',
        '/tag',
        '/test-auth',
        '/library',
        '/debug-auth',
    ]

    // 公開的 API 路由（不需要認證）
    const publicApiPaths = [
        '/api/reactions/count',
        '/api/reactions/check',
        '/api/auth/session',
        '/api/debug/cookies',
        '/api/topics',
        '/api/threads',
        '/api/cards',
        '/api/comments',
        '/api/explore',
        '/api/trending-topics',
        '/api/subtopics',
    ]

    // 檢查是否為公開路徑
    const isPublicPath = publicPaths.some(path =>
        pathname === path || pathname.startsWith(`${path}/`)
    )

    // 檢查是否為公開 API
    const isPublicApi = publicApiPaths.some(path =>
        pathname === path || pathname.startsWith(`${path}/`)
    )

    // 靜態資源不需要驗證
    const isStaticResource = pathname.startsWith('/_next') ||
        pathname.startsWith('/favicon') ||
        pathname.includes('.') && !pathname.startsWith('/api/')

    // 如果是公開路徑、公開 API 或靜態資源，直接返回
    if (isPublicPath || isPublicApi || isStaticResource) {
        return response
    }

    // 創建 Supabase 客戶端
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
        console.error('Missing Supabase environment variables in middleware')
        return response
    }

    const supabase = createServerClient(supabaseUrl, supabaseKey, {
        cookies: {
            getAll() {
                const allCookies = request.cookies.getAll()
                console.log('Middleware cookies getAll:', {
                    count: allCookies.length,
                    cookies: allCookies.map(c => ({
                        name: c.name,
                        hasValue: !!c.value,
                        valueLength: c.value.length
                    }))
                })
                return allCookies
            },
            setAll(cookiesToSet) {
                console.log('Middleware cookies setAll:', cookiesToSet.map(c => ({
                    name: c.name,
                    hasValue: !!c.value,
                    valueLength: c.value?.length || 0
                })))
                cookiesToSet.forEach(({ name, value, options }) => {
                    request.cookies.set(name, value)
                    response.cookies.set(name, value, {
                        ...options,
                        httpOnly: false, // 確保客戶端可以讀取
                        secure: process.env.NODE_ENV === 'production',
                        sameSite: 'lax',
                        path: '/'
                    })
                })
            },
        },
    })

    try {
        // 使用更安全的 getUser() 方法
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        const cookieNames = request.cookies.getAll().map(c => c.name)
        const authCookies = request.cookies.getAll().filter(c => c.name.includes('sb-'))

        console.log(`Middleware auth check for ${pathname}:`, {
            hasUser: !!user,
            userError: userError?.message,
            userId: user?.id?.slice(0, 8),
            cookieCount: request.cookies.getAll().length,
            authCookieCount: authCookies.length,
        })

        if (userError) {
            console.error('Middleware user error:', userError)
        }

        if (!user) {
            console.log(`No user found, redirecting ${pathname} to login`)

            // 如果是 API 路由，返回 401 而不是重定向
            if (pathname.startsWith('/api/')) {
                return NextResponse.json(
                    { error: 'Unauthorized' },
                    { status: 401 }
                )
            }

            // 頁面路由重定向到登入頁
            const redirectUrl = new URL('/auth/login', request.url)
            redirectUrl.searchParams.set('redirectTo', pathname)
            return NextResponse.redirect(redirectUrl)
        }

        // 有效的用戶，讓請求繼續
        console.log(`Valid user found for ${pathname}, user: ${user.id.slice(0, 8)}`)

    } catch (error) {
        console.error('Middleware error:', error)
        // API 路由發生錯誤時返回 500
        if (pathname.startsWith('/api/')) {
            return NextResponse.json(
                { error: 'Internal Server Error' },
                { status: 500 }
            )
        }
        // 頁面路由發生錯誤時讓請求繼續
        console.log('Middleware error, allowing request to continue')
    }

    return response
}

// 只匹配需要驗證的路徑（排除公開的 API）
export const config = {
    matcher: [
        '/profile/:path*',
        '/submit/:path*',
        '/my-posts/:path*',
        '/admin/:path*',
        '/api/reactions/route',    // 只保護創建/更新/刪除反應的路由
        '/api/auth/protected/:path*',
    ],
} 