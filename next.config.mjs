/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 為 API 文檔頁面配置，減少 React Strict Mode 警告
  experimental: {
    // 關閉對第三方庫的嚴格模式檢查
    strictNextHead: false,
  },
  // 配置 webpack 來忽略特定的警告
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // 抑制 React 生命週期警告
      config.resolve.alias = {
        ...config.resolve.alias,
        'react/jsx-runtime': 'react/jsx-runtime',
      }
    }
    return config
  },
}

export default nextConfig
