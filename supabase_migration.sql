-- ===============================
-- Supabase Console 執行的 SQL
-- 添加缺失的欄位來支援草稿和待審核功能
-- ===============================

-- 1. 為 CARDS 表添加缺失的欄位
ALTER TABLE public.cards 
ADD COLUMN IF NOT EXISTS status text CHECK (status IN ('draft', 'pending', 'published', 'rejected')) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now(),
ADD COLUMN IF NOT EXISTS published_at timestamptz;

-- 2. 為 THREADS 表添加缺失的欄位  
ALTER TABLE public.threads
ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now(),
ADD COLUMN IF NOT EXISTS published_at timestamptz;

-- 3. 更新 THREADS 表的 status 欄位約束（如果需要）
-- 先檢查現有的約束，如果不符合需求則更新
ALTER TABLE public.threads 
DROP CONSTRAINT IF EXISTS threads_status_check;

ALTER TABLE public.threads 
ADD CONSTRAINT threads_status_check 
CHECK (status IN ('draft', 'published', 'closed', 'archived'));

-- 4. 更新 THREADS 表的 semantic_type 欄位約束
ALTER TABLE public.threads 
DROP CONSTRAINT IF EXISTS threads_semantic_type_check;

ALTER TABLE public.threads 
ADD CONSTRAINT threads_semantic_type_check 
CHECK (semantic_type IN ('question', 'brainstorm', 'chat'));

-- 5. 為現有的已發布討論設定 published_at 時間戳（可選）
UPDATE public.threads 
SET published_at = created_at 
WHERE status = 'published' AND published_at IS NULL;

-- 6. 建立索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_cards_status ON public.cards(status);
CREATE INDEX IF NOT EXISTS idx_cards_author_status ON public.cards(author_id, status);
CREATE INDEX IF NOT EXISTS idx_threads_status ON public.threads(status);
CREATE INDEX IF NOT EXISTS idx_threads_author_status ON public.threads(author_id, status);

-- 7. 建立 updated_at 自動更新的觸發器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為 CARDS 表建立觸發器
DROP TRIGGER IF EXISTS update_cards_updated_at ON public.cards;
CREATE TRIGGER update_cards_updated_at 
    BEFORE UPDATE ON public.cards 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 為 THREADS 表建立觸發器  
DROP TRIGGER IF EXISTS update_threads_updated_at ON public.threads;
CREATE TRIGGER update_threads_updated_at 
    BEFORE UPDATE ON public.threads 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 